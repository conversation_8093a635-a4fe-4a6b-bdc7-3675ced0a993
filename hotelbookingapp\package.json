{"name": "hotelbookingapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "server": "json-server --watch db.json", "start": "concurrently \"npm run dev \"  \"npm run server\""}, "dependencies": {"@tanstack/react-query": "^5.59.0", "concurrently": "^8.2.2", "json-server": "^1.0.0-beta.1", "leaflet": "^1.9.4", "leaflet-defaulticon-compatibility": "^0.1.2", "leaflet-routing-machine": "^3.2.12", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.2.1", "react-leaflet": "^4.2.1", "react-router-dom": "^6.25.1", "uuid": "^10.0.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.40", "tailwindcss": "^3.4.7", "vite": "^5.2.0"}}