@import url('https://fonts.googleapis.com/css2?family=Playwrite+AT:ital,wght@0,100..400;1,100..400&family=Sevillana&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Gupter:wght@400;500;700&family=Playwrite+AT:ital,wght@0,100..400;1,100..400&family=Sevillana&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body{
    font-family: "Gupter", serif;
}
td{
    border-left: 2px solid #111827;
    padding: 10px;
}

.loader {
    height: 9em;
    width: 9em;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.circle {
    position: absolute;
    height: 80%;
    width: 80%;
    border-radius: 50%;
    border-style: solid;
}

.white {
    border-width: 3px 3px 0 0;
    border-color: black black transparent transparent;
    animation: 1s rotate-white linear infinite;
}

.red {
    border-width: 0 0 3px 3px;
    border-color: transparent transparent black black;
    animation: 1s rotate-red linear infinite;
}

.orange {
    border-width: 0 3px 3px 0;
    border-color: transparent black black transparent;
    animation: 1s rotate-orange linear infinite;
}

.yellow {
    border-width: 3px 0 0 3px;
    border-color: black transparent transparent black;
}

@keyframes rotate-white {
    from {
        transform: rotateX(45deg) rotateY(-35deg) rotateZ(0deg);
    }
    to {
        transform: rotateX(45deg) rotateY(-35deg) rotateZ(360deg);
    }
}

@keyframes rotate-red {
    from {
        transform: rotateX(45deg) rotateY(35deg) rotateZ(0deg);
    }
    to {
        transform: rotateX(45deg) rotateY(35deg) rotateZ(360deg);
    }
}

@keyframes rotate-orange {
    from {
        transform: rotateX(70deg) rotateZ(0deg);
    }
    to {
        transform: rotateX(70deg) rotateZ(360deg);
    }
}

@keyframes rotate-yellow {
    from {
        transform: rotateY(70deg) rotateZ(0deg);
    }
    to {
        transform: rotateY(70deg) rotateZ(360deg);
    }
}
