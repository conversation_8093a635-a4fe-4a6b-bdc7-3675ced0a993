const jwt = require("jsonwebtoken")
const User = require("../models/userModel")

const auth = async (req,res,next) =>{
    const token = req.header("auth-token")
    if (!token) {
        res.json({message: "Unauthorized access"})
    }else{
        try{
            const decoded = jwt.verify(token, process.env.JWT_SECRET_KEY)
            const user = await User.findById(decoded.id).select("-password")
            req.user = user
            next()
        }catch(error){
            if(error.name === "TokenExpiredError"){
                return res.status(401).json({success:false, message: "session expired"})
            }
            if(error.name === "JsonWebTokenError"){
                return res.status(401).json({success:false, message: "Invalid token, please login again"})
            }
            console.log(error);
        }
    }

    
}


const admin = async(req,res,next)=>{
    if (req.user.role !== "admin") {
        res.json("Access Denied")
    }else{
        next()
    }

    
}

module.exports = {auth, admin}